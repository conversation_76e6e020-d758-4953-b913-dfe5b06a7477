module transaction();
reg [1:0] tx_modeo;
reg [28:0] tx_ido;
reg [3:0] tx_dlco;
reg [63:0] tx_datao;
reg [9:0] tx_delayo;
reg tx_ready;
//******************************************
//**************  manual tx ****************
//******************************************
task tx_gen_xact_with_manual;
    input [1:0] tx_mode;
    input [28:0] tx_id;
    input [3:0] tx_dlc;
    input [63:0] tx_data;
    begin
        tx_modeo=tx_mode;
        tx_ido  =tx_id;
        tx_dlco =tx_dlc;
        tx_datao=tx_data;
        tx_delayo={$random}%1024;
    end
endtask
//******************************************
//**************  random tx ****************
//******************************************
integer i;
task tx_gen_xact_with_random;
    begin
        tx_ido[28]=0;
        tx_modeo={$random} %4;
        for(i=0;i<7;i=i+1)begin
            tx_ido[4*i+:4]  ={$random} %16;
        end
        for(i=0;i<16;i=i+1)begin
            tx_datao[4*i+:4] ={$random} %16;
        end
        tx_dlco={$random} %9;
        tx_delayo={$random}%1024;
    end
endtask
//tx task
task tx_set_xact_ready;
	begin
		tx_ready=1;
	end
endtask

task tx_wait_xact_ready;
	begin
		wait(tx_ready==1);
	end
endtask

task tx_set_xact_finish;
	begin
		tx_ready=0;
	end
endtask

task tx_wait_xact_finish;
	begin
		wait(tx_ready==1'b0);
	end
endtask






















endmodule