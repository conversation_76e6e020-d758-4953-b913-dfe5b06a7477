`include "head.v"
module driver(
    input sys_clk,
    input rst,
    input tx_busy,
    output reg [1:0] tx_mode,
    output reg [28:0] tx_id,
    output reg [3:0] tx_dlc,
    output reg [63:0] tx_data,
    output reg tx_en

);

initial begin
    tx_mode=0;
    tx_id  =0;
    tx_dlc =0;
    tx_data=0;
    tx_en=0;
    forever begin
        `DRV_XACT.tx_wait_xact_ready;
        if(!tx_busy)begin
            @(posedge sys_clk);
            tx_en=1'b1;
            tx_mode=`DRV_XACT.tx_modeo;
            tx_id  =`DRV_XACT.tx_ido;
            tx_dlc =`DRV_XACT.tx_dlco;
            tx_data=`DRV_XACT.tx_datao;
        end            
        @(posedge sys_clk);
        tx_en=1'b0;
        @(negedge tx_busy);
        repeat(`DRV_XACT.tx_delayo)@(posedge sys_clk);
        `DRV_XACT.tx_set_xact_finish;
    end



end
























endmodule