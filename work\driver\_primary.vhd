library verilog;
use verilog.vl_types.all;
entity driver is
    port(
        sys_clk         : in     vl_logic;
        rst             : in     vl_logic;
        tx_busy         : in     vl_logic;
        tx_mode         : out    vl_logic_vector(1 downto 0);
        tx_id           : out    vl_logic_vector(28 downto 0);
        tx_dlc          : out    vl_logic_vector(3 downto 0);
        tx_data         : out    vl_logic_vector(63 downto 0);
        tx_en           : out    vl_logic
    );
end driver;
