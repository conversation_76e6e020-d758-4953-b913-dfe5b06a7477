library verilog;
use verilog.vl_types.all;
entity CAN_rx is
    generic(
        IDEL            : vl_logic_vector(0 to 3) := (Hi0, Hi0, Hi0, Hi0);
        RX_ID1          : vl_logic_vector(0 to 3) := (Hi0, Hi0, Hi0, Hi1);
        RX_RTR          : vl_logic_vector(0 to 3) := (Hi0, Hi0, Hi1, Hi0);
        RX_IDE          : vl_logic_vector(0 to 3) := (Hi0, Hi0, Hi1, Hi1);
        RX_R0           : vl_logic_vector(0 to 3) := (Hi0, Hi1, Hi0, Hi0);
        RX_ID2          : vl_logic_vector(0 to 3) := (Hi0, Hi1, Hi0, Hi1);
        \RX_DLC\        : vl_logic_vector(0 to 3) := (Hi0, Hi1, Hi1, Hi0);
        RX_RTR2         : vl_logic_vector(0 to 3) := (Hi0, Hi1, Hi1, Hi1);
        RX_IDE2         : vl_logic_vector(0 to 3) := (Hi1, Hi0, Hi0, Hi0);
        \RX_DATA\       : vl_logic_vector(0 to 3) := (Hi1, Hi0, Hi0, Hi1);
        RX_CRC          : vl_logic_vector(0 to 3) := (Hi1, Hi0, Hi1, Hi0);
        RX_CRC_DEL      : vl_logic_vector(0 to 3) := (Hi1, Hi0, Hi1, Hi1);
        SEND_ACK        : vl_logic_vector(0 to 3) := (Hi1, Hi1, Hi0, Hi0);
        RX_ACK_DEL      : vl_logic_vector(0 to 3) := (Hi1, Hi1, Hi0, Hi1);
        RX_EOF          : vl_logic_vector(0 to 3) := (Hi1, Hi1, Hi1, Hi0)
    );
    port(
        sys_clk         : in     vl_logic;
        rst             : in     vl_logic;
        ce_16           : in     vl_logic;
        rx_in           : in     vl_logic;
        err_flag        : out    vl_logic;
        ack_flag        : out    vl_logic;
        rx_en           : out    vl_logic;
        rx_mode         : out    vl_logic_vector(1 downto 0);
        rx_ID           : out    vl_logic_vector(28 downto 0);
        rx_data         : out    vl_logic_vector(63 downto 0);
        rx_dlc          : out    vl_logic_vector(3 downto 0)
    );
    attribute mti_svvh_generic_type : integer;
    attribute mti_svvh_generic_type of IDEL : constant is 1;
    attribute mti_svvh_generic_type of RX_ID1 : constant is 1;
    attribute mti_svvh_generic_type of RX_RTR : constant is 1;
    attribute mti_svvh_generic_type of RX_IDE : constant is 1;
    attribute mti_svvh_generic_type of RX_R0 : constant is 1;
    attribute mti_svvh_generic_type of RX_ID2 : constant is 1;
    attribute mti_svvh_generic_type of \RX_DLC\ : constant is 1;
    attribute mti_svvh_generic_type of RX_RTR2 : constant is 1;
    attribute mti_svvh_generic_type of RX_IDE2 : constant is 1;
    attribute mti_svvh_generic_type of \RX_DATA\ : constant is 1;
    attribute mti_svvh_generic_type of RX_CRC : constant is 1;
    attribute mti_svvh_generic_type of RX_CRC_DEL : constant is 1;
    attribute mti_svvh_generic_type of SEND_ACK : constant is 1;
    attribute mti_svvh_generic_type of RX_ACK_DEL : constant is 1;
    attribute mti_svvh_generic_type of RX_EOF : constant is 1;
end CAN_rx;
