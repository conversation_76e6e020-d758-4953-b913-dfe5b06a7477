library verilog;
use verilog.vl_types.all;
entity CAN_controller is
    port(
        sys_clk         : in     vl_logic;
        rst             : in     vl_logic;
        rx_in           : in     vl_logic;
        rx_en           : out    vl_logic;
        err_flag        : out    vl_logic;
        rx_mode         : out    vl_logic_vector(1 downto 0);
        rx_ID           : out    vl_logic_vector(28 downto 0);
        rx_data         : out    vl_logic_vector(63 downto 0);
        rx_dlc          : out    vl_logic_vector(3 downto 0);
        tx_mode         : in     vl_logic_vector(1 downto 0);
        tx_id           : in     vl_logic_vector(28 downto 0);
        tx_dlc          : in     vl_logic_vector(3 downto 0);
        tx_data         : in     vl_logic_vector(63 downto 0);
        tx_en           : in     vl_logic;
        tx_out          : out    vl_logic;
        tx_busy         : out    vl_logic
    );
end CAN_controller;
