//`timescale 1ns/1ps
module tb ();
// `define clk_period_50m 20
`define clk_period_36m 27.78           //1000/36=27.78
reg sys_clk;    //36mhz
reg rst;

wire rx_in;
wire rx_en;
wire err_flag;
wire [1:0] rx_mode;
wire [3:0] rx_dlc;
wire [28:0] rx_ID;
wire [63:0] rx_data;

wire ce_16;
wire [1:0]  tx_mode;
wire [28:0] tx_id;
wire [3:0]  tx_dlc;
wire [63:0] tx_data;
wire tx_en;

//wire rx_in;
wire tx_out;
wire tx_busy;
// DUV
CAN_controller DUV(
    .sys_clk(sys_clk),
    .rst(rst),


    .rx_in(tx_out),
    .rx_en(rx_en),
    .err_flag(err_flag),
    .rx_mode(rx_mode),
    .rx_ID(rx_ID),
    .rx_data(rx_data),
    .rx_dlc(rx_dlc),

    .tx_mode(tx_mode), //bit[0] 0 数据帧,1 遥控帧; bit[1] 0 标准格式，1扩展格式
    .tx_id(tx_id),
    .tx_dlc(tx_dlc),
    .tx_data(tx_data),
    .tx_en(tx_en),

    .tx_out(tx_out),
    .tx_busy(tx_busy)
);
driver drv1(
    .sys_clk(sys_clk),
    .rst(rst),
    .tx_busy(tx_busy),
    .tx_mode(tx_mode),
    .tx_id(tx_id),
    .tx_dlc(tx_dlc),
    .tx_data(tx_data),
    .tx_en(tx_en)

);
transaction drv_xact();

testcase1 test1();

always #(`clk_period_36m/2) sys_clk=~sys_clk;

initial begin
    sys_clk=0;
    rst=1;
    #100;
    rst=0;
end
initial begin
	#200;
	test1.start1;
    #500000;
    $stop;
end
endmodule