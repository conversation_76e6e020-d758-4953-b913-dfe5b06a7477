module CAN_controller (
    input wire sys_clk,
    input wire rst,

    input  wire rx_in,          //串行数据输入
    output wire rx_en,          //包接收结束标志
    output wire err_flag,       //接收错误标志
    output wire [1:0] rx_mode,  //接收到的包的模式bit[0] 0 数据帧,1 遥控帧; bit[1] 0 标准格式，1扩展格式
    output wire [28:0] rx_ID,   //接收到的设备ID
    output wire [63:0] rx_data, //接收到的数据
    output wire [3:0] rx_dlc,   //接收到的数据的长度

    input wire [1:0] tx_mode,   //发送模式：bit[0] 0 数据帧,1 遥控帧; bit[1] 0 标准格式，1扩展格式
    input wire [28:0] tx_id,    //发送设备ID
    input wire [3:0] tx_dlc,    //发送数据长度
    input wire [63:0] tx_data,  //发送数据，从低到高进行填充，1个包最多支持8byte
    input wire tx_en,           //新的发送数据包使能，在tx_busy为低时，拉高1次进行数据发送
    output wire tx_out,         //串行数据输出
    output wire tx_busy         //发送繁忙标志
);
wire tx_out1;
wire ack_flag;
assign tx_out=ack_flag?tx_out1:ack_flag;
wire ce_16;
baud_gen u_baud_gen(
	.clk(sys_clk), 
    .rst(rst), 
	.ce_16(ce_16), 
    .baud_freq(12'h060), 
    .baud_limit(16'h0211) 
);

CAN_tx u_CAN_tx(
    .sys_clk(sys_clk),
    .rst(rst),
    .ce_16(ce_16),
    .tx_mode(tx_mode), //bit[0] 0 数据帧,1 遥控帧; bit[1] 0 标准格式，1扩展格式
    .tx_id(tx_id),
    .tx_dlc(tx_dlc),
    .tx_data(tx_data),
    .tx_en(tx_en),

    .rx_in(rx_in),
    .tx_out(tx_out1),
    .tx_busy(tx_busy)
);

CAN_rx u_CAN_rx(
    .sys_clk(sys_clk),
    .rst(rst),
    .ce_16(ce_16),
    .rx_in(rx_in),

    .err_flag(err_flag),//当前是1bit，后续可以扩展为多bit，分类不同的错误
    .ack_flag(ack_flag),
    .rx_en(rx_en),
    .rx_mode(rx_mode),
    .rx_ID(rx_ID),
    .rx_data(rx_data),
    .rx_dlc(rx_dlc)
);




endmodule