module CAN_rx (
    input wire sys_clk,
    input wire rst,
    input wire ce_16,
    input wire rx_in,


    output reg err_flag,//当前是1bit，后续可以扩展为多bit，分类不同的错误
    output reg ack_flag,
    output reg rx_en,
    output wire [1:0] rx_mode,
    output reg [28:0] rx_ID,
    output reg [63:0] rx_data,
    output reg [3:0]  rx_dlc
);
//***************************************//
//************STATE PARAMETER************//
//***************************************//
reg [3:0] rx_state;
parameter IDEL      =4'd0;
parameter RX_ID1    =4'd1;
parameter RX_RTR    =4'd2;
parameter RX_IDE    =4'd3;
parameter RX_R0     =4'd4;
parameter RX_ID2    =4'd5;
parameter RX_DLC    =4'd6;
parameter RX_RTR2   =4'd7;
parameter RX_IDE2   =4'd8;
parameter RX_DATA   =4'd9;
parameter RX_CRC    =4'd10;
parameter RX_CRC_DEL=4'd11;
parameter SEND_ACK  =4'd12;
parameter RX_ACK_DEL=4'd13;
parameter RX_EOF    =4'd14;
//***************************************//
//************  REG AND WIRE ************//
//***************************************//

reg [3:0] count16;
reg [7:0] bit_count;
reg [2:0] EOF_count;
reg [1:0] mode_buffer;
wire s_flag;//位填充标志位
reg [2:0] s_bit_count;//相同bit位计数器
reg rx_in_d1;

reg [15:0] CRC_buffer;//对数据进行CRC校验存储器，与接收到的CRC信息比对
reg RX_DATA_flag;
reg RX_DATA_flag_d1;
reg rx_data_flag_p1;
reg EOF_flag;
wire ce_1;
wire ce_1_center;
wire [15:0] rx_CRC;

assign ce_1=(count16==4'b1111)&ce_16;
wire [7:0] DATA_length;
assign DATA_length=(rx_dlc<<3)-1;
assign rx_mode=mode_buffer;
assign s_flag=s_bit_count[2];
//***************************************//
//************  STATE MACHINE************//
//***************************************//
always @(posedge sys_clk or posedge rst) begin
    if(rst)begin
        rx_state        <=IDEL  ;
        bit_count       <=8'd0  ;
        EOF_count       <=3'd0  ;
        rx_ID           <=29'd0 ;
        rx_data         <=64'd0 ;
        rx_dlc          <=4'd0  ;
        mode_buffer     <=2'd0  ;
        err_flag        <=1'b0  ;
        ack_flag        <=1'b1  ;
        CRC_buffer      <=16'd0 ;
        rx_en           <=1'b0  ;
        RX_DATA_flag    <=1'b0  ;
        EOF_flag        <=1'b0  ;
    end
    else if(ce_1 &&(!s_flag) &&(!err_flag))begin
        case(rx_state)
            IDEL:begin
                if(!rx_in)begin
                    rx_state<= RX_ID1;
                    EOF_flag        <=1'b0  ;
                end 
                else begin
                    rx_state<=IDEL;
                end
                bit_count       <=8'd0  ;
                EOF_count       <=3'd0  ;
                rx_ID           <=29'd0 ;
                rx_data         <=64'd0 ;
                rx_dlc          <=4'd0  ;
                mode_buffer     <=2'd0  ;
                err_flag        <=1'b0  ;
                ack_flag        <=1'b1  ;
                rx_en           <=1'b0  ;
                RX_DATA_flag    <=1'b0  ;        
            end
            RX_ID1:begin
                if(bit_count==8'd10)begin
                    rx_state        <=  RX_RTR;
                    rx_ID[bit_count]<=  rx_in;
                    bit_count       <=  8'd0;
                end
                else begin
                    rx_state        <=  RX_ID1;
                    rx_ID[bit_count]<=  rx_in;
                    bit_count       <=  bit_count+1'b1;
                end
            end
            RX_RTR:begin
                rx_state        <= RX_IDE;
                mode_buffer[0]  <= rx_in;
            end
            RX_IDE:begin
                if((mode_buffer[0]==0)&&(rx_in==0))begin//标准数据帧
                    mode_buffer[1]<= 0;
                    rx_state      <=RX_R0;
                end 
                else if((mode_buffer[0]==0)&&(rx_in==1))begin
                    err_flag<=1'b1;                 //错误
                end
                else if((mode_buffer[0]==1)&&(rx_in==0))begin//标准遥控帧
                    mode_buffer<=2'b01;
                    rx_state   <=RX_R0;
                end
                else if((mode_buffer[0]==1)&&(rx_in==1))begin//扩展模式
                    mode_buffer[1]<=1'b1;
                    rx_state   <=RX_ID2;
                end
            end
            RX_R0:begin
                if(rx_in)
                    err_flag<=1'b1;
                else
                    rx_state<=RX_DLC;
            end
            RX_ID2:begin
                if(bit_count==8'd17)begin
                    rx_state        <=  RX_RTR2;
                    rx_ID[bit_count+11]<=  rx_in;
                    bit_count       <=  8'd0;
                end
                else begin
                    rx_state        <=  RX_ID2;
                    rx_ID[bit_count+11]<=  rx_in;
                    bit_count       <=  bit_count+1'b1;
                end
            end
            RX_RTR2:begin
                if(rx_in)begin
                    mode_buffer[0]=1'b1;//扩展遥控帧
                end
                else begin
                    mode_buffer[0]=1'b0;//扩展数据帧
                end
                rx_state<=RX_IDE2;
            end
            RX_IDE2:begin
                if(rx_in)
                    err_flag<=1'b1;
                else
                    rx_state<=RX_R0;
            end
            RX_DLC:begin
                if(bit_count==8'd3)begin
                    if((mode_buffer[0])||((rx_dlc[2:0]==3'b0)&&(rx_in==1'b0)))begin
                        RX_DATA_flag<=1'b1;
                        rx_state    <= RX_CRC             ;//遥控帧：跳过数据
                    end 
                    else begin
                        rx_state    <= RX_DATA            ;//数据帧
                    end
                    rx_dlc[bit_count]<= rx_in             ;
                    bit_count   <= 8'd0                   ;
                end
                else begin
                    rx_state    <= RX_DLC                 ;
                    rx_dlc[bit_count]<= rx_in             ;
                    bit_count   <= bit_count+1'b1         ;
                end 
            end
            RX_DATA:begin
                if(bit_count==DATA_length)begin
                    rx_state            <=RX_CRC;
                    rx_data[bit_count]  <=rx_in;
                    bit_count           <=8'd0;
                    RX_DATA_flag        <=1'b1;
                end
                else begin
                    rx_state            <=RX_DATA;
                    rx_data[bit_count]  <=rx_in;
                    bit_count           <=bit_count+1'b1;
                end 
            end
            RX_CRC:begin
                RX_DATA_flag        <=1'b0;
                if((bit_count==8'd15)&&(CRC_buffer[bit_count]==rx_in))begin
                    rx_state    <= RX_CRC_DEL;
                    bit_count   <= 8'd0;
                    ack_flag    <= 1'b0;
                end 
                else if(CRC_buffer[bit_count]==rx_in)begin
                    rx_state    <= RX_CRC;
                    bit_count   <= bit_count+1'b1;
                end
                else
                    err_flag<=1'b1;//校验出错
            end
            RX_CRC_DEL:begin
                ack_flag<=1'b1;
                if(rx_in)
                    rx_state<= SEND_ACK;
                else
                    err_flag<=1'b1;//分隔符必须为高电平   
            end
            SEND_ACK:begin
                rx_state<=RX_ACK_DEL;
            end
            RX_ACK_DEL:begin
                if(rx_in)
                    rx_state<=RX_EOF;
                else
                    err_flag<=1'b1;//分隔符必须为高电平
            end
            RX_EOF:begin
                EOF_flag<=1'b1;
                if((EOF_count==3'd6)&&(rx_in))begin
                    rx_state<=IDEL;
                    EOF_count<=3'd0;
                    rx_en   <=1'b1;
                end 
                else if(rx_in) begin
                    rx_state<=RX_EOF;
                    EOF_count<=EOF_count+1'b1;
                end
                else
                    err_flag<=1'b1;//EOF必须为高电平
            end
            default:rx_state<=IDEL;
        endcase
    end
    else if(err_flag)
        rx_state        <=IDEL;
        err_flag        <=1'b0;
end
//***************************************//
//************  BAUD CONTROL ************//
//***************************************//
// tx_busy flag 
always @ (posedge sys_clk or posedge rst)begin
	if (rst) 
		count16 <= 4'b0;
	else if (ce_16)
		count16 <= count16 + 1'b1;
    else if (err_flag)
        count16 <= 4'b0;
end 

always@(posedge sys_clk or posedge rst)begin
    if(rst)
        rx_in_d1<=1'b0;
    else if(ce_1)
        rx_in_d1<=rx_in;
    else if(err_flag)
        rx_in_d1<=1'b0;
end
always@(posedge sys_clk or posedge rst)begin
    if(rst)
        s_bit_count<=3'd0;
    else if((s_bit_count==3'd4) && ce_1)
        s_bit_count<=3'd0;
    else if((rx_in==rx_in_d1) && ce_1 &&(!EOF_flag))
        s_bit_count<=s_bit_count+1'b1;
    else if(ce_1 || err_flag)
        s_bit_count<=3'd0;
end
always@(posedge sys_clk or posedge rst)begin
    if(rst)
        CRC_buffer<=16'd0;
    else if(err_flag)
        CRC_buffer<=16'd0;    
    else
        CRC_buffer<=rx_CRC;
end

always@(posedge sys_clk or posedge rst)begin
    if(rst)
        RX_DATA_flag_d1<=1'b0;
    else 
        RX_DATA_flag_d1<=RX_DATA_flag;
end
always@(posedge sys_clk or posedge rst)begin
    if(rst)
        rx_data_flag_p1<=1'b0;
    else if((!RX_DATA_flag_d1)&&RX_DATA_flag)
        rx_data_flag_p1<=1'b1;
    else
        rx_data_flag_p1<=1'b0;
end
CAN_CRC u_rx_CRC(
  .data_in(rx_data),
  .crc_en(rx_data_flag_p1),
  .crc_out(rx_CRC),
  .rst(rst),
  .clk(sys_clk)
  );
endmodule
