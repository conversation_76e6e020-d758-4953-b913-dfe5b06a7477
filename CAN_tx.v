module CAN_tx (
    input wire sys_clk,
    input wire rst,
    input wire ce_16,
    input wire [1:0] tx_mode, //bit[0] 0 数据帧,1 遥控帧; bit[1] 0 标准格式，1扩展格式
    input wire [28:0] tx_id,
    input wire [3:0]  tx_dlc,
    input wire [63:0] tx_data,
    input wire tx_en,

    input  wire rx_in,
    output wire  tx_out,
    output reg tx_busy
);
//***************************************//
//************STATE PARAMETER************//
//***************************************//
reg [3:0] tx_state;
parameter IDEL      =4'd0;
parameter TX_START  =4'd1;
parameter TX_ID1    =4'd2;
parameter TX_RTR    =4'd3;
parameter TX_IDE    =4'd4;
parameter TX_R0     =4'd5;
parameter TX_ID2    =4'd6;
parameter TX_DLC    =4'd7;
parameter TX_DATA   =4'd8;
parameter TX_CRC    =4'd9;
parameter TX_CRC_DEL=4'd10;
parameter WAIT_ACK  =4'd11;
parameter TX_ACK_DEL=4'd12;
parameter TX_EOF    =4'd13;
//***************************************//
//************  REG AND WIRE ************//
//***************************************//
reg tx_out_r;
reg [3:0] count16;
reg [7:0] bit_count;
reg [2:0] EOF_count;
reg [28:0] ID_Buffered;
reg [63:0] data_buffered;
reg [3:0] DLC_buffered;
reg [1:0] tx_mode_buffered;
reg ID2_complete;
reg [15:0] CRC_buffered;
reg [7:0] DATA_length;
reg ACK_reg;
reg tx_out_d1;
wire s_flag;//位填充标志位
reg [2:0] s_bit_count;//相同bit位计数器
reg EOF_flag;// EOF发送标志
reg TX_START_flag;//START状态开始标志
reg TX_START_flag_d1;
reg TX_START_poseflag;//捕捉TX_START_flag的上升沿
wire ce_1;
assign ce_1=(count16==4'b1111)&ce_16;   
assign s_flag=s_bit_count[2];
assign tx_out=s_flag?(!tx_out_d1):tx_out_r;
wire [15:0] tx_CRC;
//***************************************//
//************  STATE MACHINE************//
//***************************************//
always @(posedge sys_clk or posedge rst) begin
    if(rst)begin
        tx_state        <=IDEL  ;
        tx_out_r        <=1'b1  ;
        bit_count       <=8'd0  ;
        EOF_count       <=3'd0  ;
        ID_Buffered     <=29'd0 ;
        data_buffered   <=64'd0 ;
        DLC_buffered    <=4'd0  ;
        tx_mode_buffered<=2'd0  ;
        ID2_complete    <=1'b0  ;
        CRC_buffered    <=16'b0 ;
        DATA_length     <=8'd0  ;
        ACK_reg         <=1'b0  ;
        EOF_flag        <=1'b0  ;
        TX_START_flag   <=  1'b0    ;
    end
    else if(tx_busy && ce_1 && (!s_flag))begin
        case(tx_state)
            IDEL:begin
                tx_out_r        <=  1'b1    ;
                tx_state        <=  TX_START;
                bit_count       <=  8'd0    ;
                EOF_count       <=  3'd0    ;
                ID_Buffered     <=  29'd0   ;
                data_buffered   <=  8'd0    ;
                DLC_buffered    <=  4'd0    ;
                tx_mode_buffered<=  2'd0    ;
                ID2_complete    <=  1'b0    ;
                CRC_buffered    <=  16'b0   ;
                DATA_length     <=  4'd0    ;
                ACK_reg         <=  1'b1    ;
                TX_START_flag   <=  1'b0    ;
            end
            TX_START:begin
                EOF_flag        <=  1'b0    ;
                tx_out_r        <=  1'b0   ;
                tx_state        <=  TX_ID1 ;
                ID_Buffered     <=  tx_id  ;    //ID寄存器
                data_buffered   <=  tx_data;    //数据寄存器    
                DLC_buffered    <=  tx_dlc ;    //数据长度寄存器
                tx_mode_buffered<=  tx_mode;    //发送模式寄存器
                DATA_length     <=  tx_dlc*5'd8;
                TX_START_flag   <=  1'b1    ;
            end
            TX_ID1:begin
                TX_START_flag    <=  1'b0    ;
                CRC_buffered    <=  tx_CRC ;            //CRC寄存器
                if(bit_count==8'd10)begin
                    tx_state    <= TX_RTR;
                    tx_out_r      <= ID_Buffered[bit_count];
                    bit_count   <= 8'd0;
                end
                else begin
                    tx_state    <= TX_ID1 ;
                    tx_out_r      <= ID_Buffered[bit_count];
                    bit_count   <= bit_count+1'b1;
                end 
            end
            TX_RTR:begin
                if(!ID2_complete)begin
                    if(tx_mode_buffered[0])begin
                        tx_out_r      <=1'b1;     //遥控帧RTR
                    end 
                    else begin 
                        if(tx_mode_buffered[1])begin
                            tx_out_r      <=1'b1;     //扩展模式SRR
                        end 
                        else begin
                            tx_out_r      <=1'b0;     //标准模式RTR
                        end
                    end
                end
                else begin
                    if(tx_mode_buffered[0])begin
                        tx_out_r      <=1'b1;     //遥控帧RTR
                    end 
                    else begin
                        tx_out_r      <=1'b0;     //标准模式RTR
                    end
                end
                tx_state        <=TX_IDE;
            end
            TX_IDE:begin
                if(tx_mode_buffered[1])begin
                    if(ID2_complete)begin
                        tx_out_r      <=1'b0;     //第二次扩展格式IDE
                        tx_state    <=TX_R0;
                    end 
                    else begin
                        tx_out_r      <=1'b1;     //第一次扩展格式IDE
                        tx_state<=TX_ID2;
                    end
                end
                else begin                  //标准格式
                    tx_out_r      <=1'b0;     
                    tx_state    <=TX_R0;
                end
            end
            TX_ID2:begin
                if(bit_count==8'd17)begin
                    tx_state    <= TX_RTR                ;
                    tx_out_r      <= ID_Buffered[bit_count+11];
                    ID2_complete<= 1'b1                  ;
                    bit_count   <= 8'd0                  ;
                end
                else begin
                    tx_state    <= TX_ID2                ;
                    tx_out_r      <= ID_Buffered[bit_count+11];
                    bit_count   <= bit_count+1'b1        ;
                end 
            end
            TX_R0:begin
                tx_out_r  <=1'b0  ;
                tx_state<=TX_DLC;
            end
            TX_DLC:begin
                if(bit_count==8'd3)begin
                    if((tx_mode_buffered[0])||(DATA_length==0))begin
                        tx_state    <= TX_CRC             ;//遥控帧：跳过数据发送
                    end 
                    else begin
                        tx_state    <= TX_DATA            ;//数据帧
                    end
                    tx_out_r      <= DLC_buffered[bit_count];
                    bit_count   <= 8'd0                   ;
                end
                else begin
                    tx_state    <= TX_DLC                 ;
                    tx_out_r      <= DLC_buffered[bit_count];
                    bit_count   <= bit_count+1'b1         ;
                end 
            end
            TX_DATA:begin
                if(bit_count==DATA_length-1'b1)begin
                    tx_state    <= TX_CRC                   ;
                    tx_out_r      <= data_buffered[bit_count] ;
                    bit_count   <= 8'd0                     ;
                end
                else begin
                    tx_state    <= TX_DATA                   ;
                    tx_out_r      <= data_buffered[bit_count] ;
                    bit_count   <= bit_count+1'b1           ;
                end 
            end
            TX_CRC:begin
                if(bit_count==8'd15)begin
                    tx_state    <= TX_CRC_DEL             ;
                    tx_out_r      <= CRC_buffered[bit_count];
                    bit_count   <= 8'd0;
                end
                else begin
                    tx_state    <= TX_CRC                 ;
                    tx_out_r      <= CRC_buffered[bit_count];
                    bit_count   <= bit_count+1'b1         ;
                end
            end
            TX_CRC_DEL:begin
                tx_out_r  <= 1'b1;
                tx_state<= WAIT_ACK;
            end
            WAIT_ACK:begin
                ACK_reg <=rx_in;
                tx_state<=TX_ACK_DEL;
            end
            TX_ACK_DEL:begin
                ACK_reg <=1'b1;
                tx_out_r  <=1'b1;   
                tx_state<=TX_EOF;
            end
            TX_EOF:begin
                EOF_flag <=1'b1;
                if(EOF_count==3'd6)begin
                    tx_out_r<=1'b1;
                    tx_state<=IDEL;
                    EOF_count<=3'd0;
                    
                end 
                else begin
                    tx_out_r<=1'b1;
                    tx_state<=TX_EOF;
                    EOF_count<=EOF_count+1'b1;
                end
            end
            default:tx_state<=IDEL;
        endcase
    end
end
//***************************************//
//************  BAUD CONTROL ************//
//***************************************//
// tx_busy flag 
always @ (posedge sys_clk or posedge rst)
begin
	if (rst) 
		tx_busy <= 1'b0;
	else if ((tx_busy==1'b0) & tx_en)
		tx_busy <= 1'b1;
	else if (tx_busy & (EOF_count == 3'h6) & ce_1)
		tx_busy <= 1'b0;
end 
always @ (posedge sys_clk or posedge rst)begin
	if (rst) 
		count16 <= 4'b0;
	else if (ce_16)
		count16 <= count16 + 1'b1;
end 
//***************************************//
//************  BIT FILL     ************//
//***************************************//

always@(posedge sys_clk or posedge rst)begin
    if(rst)
        s_bit_count<=3'd0;
    else if((s_bit_count==3'd4)&&tx_busy && ce_1)//对比四次成功则表示五个重复电平
        s_bit_count<=3'd0;
    else if(EOF_flag)
        s_bit_count<=3'd0;
    else if((tx_out==tx_out_d1)&&tx_busy && ce_1 )
        s_bit_count<=s_bit_count+1'b1;
    else if(tx_busy && ce_1)
        s_bit_count<=3'd0;
end
always@(posedge sys_clk or posedge rst)begin
    if(rst)
        tx_out_d1<=1'd0;
    else if(tx_busy && ce_1)
        tx_out_d1<=tx_out;
    else if(!tx_busy)
        tx_out_d1<=1'b0;
end
//***************************************//
//************  CRC CONTROL  ************//
//***************************************//
reg [63:0] data_Mask;
reg tx_en_d1;
always@(posedge sys_clk or posedge rst)begin
    if(rst)
        tx_en_d1<=1'b0;
    else
        tx_en_d1<=tx_en;
end
always @(posedge sys_clk or posedge rst) begin
    if(rst)
        data_Mask<=64'd0;
    else if(tx_mode[0])
        data_Mask<=64'd0;
    else if(tx_en_d1)begin
        case(tx_dlc)
            4'b0000:data_Mask <= 64'h00_00_00_00_00_00_00_00;
            4'b0001:data_Mask <= 64'h00_00_00_00_00_00_00_ff;
            4'b0010:data_Mask <= 64'h00_00_00_00_00_00_ff_ff;
            4'b0011:data_Mask <= 64'h00_00_00_00_00_ff_ff_ff;
            4'b0100:data_Mask <= 64'h00_00_00_00_ff_ff_ff_ff;
            4'b0101:data_Mask <= 64'h00_00_00_ff_ff_ff_ff_ff;
            4'b0110:data_Mask <= 64'h00_00_ff_ff_ff_ff_ff_ff;
            4'b0111:data_Mask <= 64'h00_ff_ff_ff_ff_ff_ff_ff;
            4'b1000:data_Mask <= 64'hff_ff_ff_ff_ff_ff_ff_ff;
            default:data_Mask <= 64'h00_00_00_00_00_00_00_00;
        endcase
    end
end
always@(posedge sys_clk or posedge rst)begin
    if(rst)
        TX_START_flag_d1<=1'b0;
    else
        TX_START_flag_d1<=TX_START_flag;
end
always@(posedge sys_clk or posedge rst)begin
    if(rst)
        TX_START_poseflag<=1'b0;
    else if((!TX_START_flag_d1)&& TX_START_flag)
        TX_START_poseflag<=1'b1;
    else
        TX_START_poseflag<=1'b0;
end
CAN_CRC u_tx_CRC(
  .data_in(data_buffered & data_Mask),
  .crc_en(TX_START_poseflag),
  .crc_out(tx_CRC),
  .rst(rst),
  .clk(sys_clk)
  );

endmodule
