library verilog;
use verilog.vl_types.all;
entity CAN_tx is
    generic(
        IDEL            : vl_logic_vector(0 to 3) := (Hi0, Hi0, Hi0, Hi0);
        TX_START        : vl_logic_vector(0 to 3) := (Hi0, Hi0, Hi0, Hi1);
        TX_ID1          : vl_logic_vector(0 to 3) := (Hi0, Hi0, Hi1, Hi0);
        TX_RTR          : vl_logic_vector(0 to 3) := (Hi0, Hi0, Hi1, Hi1);
        TX_IDE          : vl_logic_vector(0 to 3) := (Hi0, Hi1, Hi0, Hi0);
        TX_R0           : vl_logic_vector(0 to 3) := (Hi0, Hi1, Hi0, Hi1);
        TX_ID2          : vl_logic_vector(0 to 3) := (Hi0, Hi1, Hi1, Hi0);
        \TX_DLC\        : vl_logic_vector(0 to 3) := (Hi0, Hi1, Hi1, Hi1);
        \TX_DATA\       : vl_logic_vector(0 to 3) := (Hi1, Hi0, Hi0, Hi0);
        TX_CRC          : vl_logic_vector(0 to 3) := (Hi1, Hi0, Hi0, Hi1);
        TX_CRC_DEL      : vl_logic_vector(0 to 3) := (Hi1, Hi0, Hi1, Hi0);
        WAIT_ACK        : vl_logic_vector(0 to 3) := (Hi1, Hi0, Hi1, Hi1);
        TX_ACK_DEL      : vl_logic_vector(0 to 3) := (Hi1, Hi1, Hi0, Hi0);
        TX_EOF          : vl_logic_vector(0 to 3) := (Hi1, Hi1, Hi0, Hi1)
    );
    port(
        sys_clk         : in     vl_logic;
        rst             : in     vl_logic;
        ce_16           : in     vl_logic;
        tx_mode         : in     vl_logic_vector(1 downto 0);
        tx_id           : in     vl_logic_vector(28 downto 0);
        tx_dlc          : in     vl_logic_vector(3 downto 0);
        tx_data         : in     vl_logic_vector(63 downto 0);
        tx_en           : in     vl_logic;
        rx_in           : in     vl_logic;
        tx_out          : out    vl_logic;
        tx_busy         : out    vl_logic
    );
    attribute mti_svvh_generic_type : integer;
    attribute mti_svvh_generic_type of IDEL : constant is 1;
    attribute mti_svvh_generic_type of TX_START : constant is 1;
    attribute mti_svvh_generic_type of TX_ID1 : constant is 1;
    attribute mti_svvh_generic_type of TX_RTR : constant is 1;
    attribute mti_svvh_generic_type of TX_IDE : constant is 1;
    attribute mti_svvh_generic_type of TX_R0 : constant is 1;
    attribute mti_svvh_generic_type of TX_ID2 : constant is 1;
    attribute mti_svvh_generic_type of \TX_DLC\ : constant is 1;
    attribute mti_svvh_generic_type of \TX_DATA\ : constant is 1;
    attribute mti_svvh_generic_type of TX_CRC : constant is 1;
    attribute mti_svvh_generic_type of TX_CRC_DEL : constant is 1;
    attribute mti_svvh_generic_type of WAIT_ACK : constant is 1;
    attribute mti_svvh_generic_type of TX_ACK_DEL : constant is 1;
    attribute mti_svvh_generic_type of TX_EOF : constant is 1;
end CAN_tx;
