onerror {resume}
quietly WaveActivateNextPane {} 0
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/sys_clk
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/rst
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/ce_16
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/tx_mode
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/tx_id
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/tx_dlc
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/tx_data
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/tx_en
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/rx_in
add wave -noupdate -group TX_signal -color Magenta /tb/DUV/u_CAN_tx/tx_out
add wave -noupdate -group TX_signal -color Magenta /tb/DUV/u_CAN_tx/ce_1
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/tx_busy
add wave -noupdate -group TX_signal -radix unsigned /tb/DUV/u_CAN_tx/tx_state
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/count16
add wave -noupdate -group TX_signal -radix unsigned /tb/DUV/u_CAN_tx/bit_count
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/EOF_count
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/ID_Buffered
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/data_buffered
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/DLC_buffered
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/tx_mode_buffered
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/ID2_complete
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/CRC_buffered
add wave -noupdate -group TX_signal -radix unsigned /tb/DUV/u_CAN_tx/DATA_length
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/ACK_reg
add wave -noupdate -group TX_signal -color Magenta /tb/DUV/u_CAN_tx/tx_out_d1
add wave -noupdate -group TX_signal /tb/DUV/u_CAN_tx/s_flag
add wave -noupdate -group TX_signal -radix unsigned /tb/DUV/u_CAN_tx/s_bit_count
add wave -noupdate -group TX_signal /tb/drv_xact/tx_modeo
add wave -noupdate -group TX_signal /tb/drv_xact/tx_ido
add wave -noupdate -group TX_signal -radix unsigned /tb/drv_xact/tx_dlco
add wave -noupdate -group TX_signal /tb/drv_xact/tx_datao
add wave -noupdate -group TX_signal /tb/drv_xact/tx_delayo
add wave -noupdate -group TX_signal /tb/drv_xact/tx_ready
add wave -noupdate -group TX_signal /tb/drv_xact/i
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/sys_clk
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/rst
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/ce_16
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/rx_in
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/err_flag
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/ack_flag
add wave -noupdate -expand -group RX_signal -color Magenta /tb/DUV/u_CAN_rx/rx_en
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/rx_mode
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/rx_ID
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/rx_data
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/rx_dlc
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/rx_state
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/count16
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/bit_count
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/EOF_count
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/mode_buffer
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/s_flag
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/s_bit_count
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/rx_in_d1
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/CRC_buffer
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/ce_1
add wave -noupdate -expand -group RX_signal /tb/DUV/u_CAN_rx/DATA_length
TreeUpdate [SetDefaultTree]
WaveRestoreCursors {{Cursor 1} {62794 ps} 0}
quietly wave cursor active 1
configure wave -namecolwidth 150
configure wave -valuecolwidth 100
configure wave -justifyvalue left
configure wave -signalnamewidth 1
configure wave -snapdistance 10
configure wave -datasetprefix 0
configure wave -rowmargin 4
configure wave -childrowmargin 2
configure wave -gridoffset 0
configure wave -gridperiod 1
configure wave -griddelta 40
configure wave -timeline 0
configure wave -timelineunits ps
update
WaveRestoreZoom {0 ps} {1245158 ps}
